/* BrainRipe Main Styles */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Fredoka One', cursive;
  font-weight: 400;
  margin-bottom: 0.5em;
  color: #2d3748;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
  margin-bottom: 1em;
  color: #4a5568;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-screen.hidden {
  display: none !important;
}

.loading-content {
  text-align: center;
  color: white;
}

.brain-logo {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 1rem auto;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Screen Management */
.screen {
  min-height: 100vh;
  width: 100%;
}

.screen.hidden {
  display: none !important;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Open Sans', sans-serif;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
}

.btn-secondary {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2d3748;
  box-shadow: 0 4px 15px rgba(168, 237, 234, 0.4);
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(168, 237, 234, 0.6);
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  font-size: 1.2rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 30px;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2d3748;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 15px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

/* Cards */
.card {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Progress Bars */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar.small {
  height: 6px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal.hidden {
  display: none;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 1.5rem 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 0 1.5rem 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #a0aec0;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f7fafc;
  color: #2d3748;
}

/* Notifications */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  background: white;
  border-radius: 15px;
  padding: 1rem 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 300px;
  animation: notificationSlideIn 0.3s ease;
  border-left: 4px solid #4facfe;
}

.notification-success {
  border-left-color: #48bb78;
}

.notification-error {
  border-left-color: #f56565;
}

.notification-warning {
  border-left-color: #ed8936;
}

.notification-info {
  border-left-color: #4facfe;
}

@keyframes notificationSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-icon {
  font-size: 1.2rem;
}

.notification-message {
  flex: 1;
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #a0aec0;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animations */
.points-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1500;
}

.points-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  font-weight: bold;
  color: #4facfe;
  animation: pointsFloat 2s ease-out forwards;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes pointsFloat {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -70px) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -100px) scale(0.8);
  }
}

.level-up-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: levelUpBounce 3s ease-out forwards;
}

.level-up-content {
  text-align: center;
  background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.level-up-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.level-up-text {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.level-up-level {
  font-size: 2rem;
  font-weight: bold;
}

@keyframes levelUpBounce {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  40% {
    transform: translate(-50%, -50%) scale(0.9);
  }
  60% {
    transform: translate(-50%, -50%) scale(1.1);
  }
  80% {
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #4facfe;
  animation: confettiFall 3s linear forwards;
}

@keyframes confettiFall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .notification {
    min-width: 280px;
  }
  
  .notification-container {
    right: 10px;
    left: 10px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
  
  .card {
    padding: 1rem;
    border-radius: 15px;
  }
}

/* Theme Support */
.theme-ocean {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.theme-forest {
  --primary-gradient: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
  --secondary-gradient: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
}

.theme-space {
  --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #4a6741 100%);
  --secondary-gradient: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 2px solid #4facfe;
  outline-offset: 2px;
}

/* Content card states */
.content-card.content-started {
  opacity: 0.7;
  border: 2px solid #27ae60;
  background: linear-gradient(135deg, #f8f9fa, #e8f5e8);
}

.content-card.content-started .content-title::after {
  content: " ✓";
  color: #27ae60;
  font-weight: bold;
}

.btn-disabled {
  background: #95a5a6 !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.content-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.content-link {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
}

/* Completed Tasks Screen */
.completed-tasks-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.completed-tasks-header {
  background: white;
  border-radius: 15px;
  padding: 1rem 2rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.completed-tasks-header h2 {
  margin: 0;
  color: #2c3e50;
}

.completed-stats {
  color: #7f8c8d;
  font-weight: 600;
}

.completed-tasks-main {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.completed-tasks-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.filter-btn.active {
  background: #4facfe;
  color: white;
  border-color: #4facfe;
}

.filter-btn:hover:not(.active) {
  border-color: #4facfe;
  color: #4facfe;
}

.completed-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.completed-task-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.completed-task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #27ae60;
}

.task-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.task-info {
  flex: 1;
}

.task-title {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #7f8c8d;
  flex-wrap: wrap;
}

.task-points {
  color: #27ae60;
  font-weight: 600;
}

.completion-badge {
  background: #27ae60;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.no-completed-tasks {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: #7f8c8d;
}

/* Profile Modal Preferences */
.preferences-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pref-option {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.pref-option label {
  font-weight: 600;
  color: #2c3e50;
}

.content-type-preferences {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Quit Button Styling */
.quit-btn {
  background-color: #e53e3e !important;
  border-color: #e53e3e !important;
}

.quit-btn:hover {
  background-color: #c53030 !important;
  border-color: #c53030 !important;
}

/* Badges and Rewards Modal */
.badges-rewards-modal .modal-content {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.rewards-section {
  margin-bottom: 2rem;
}

.rewards-section h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.badges-grid, .rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.badge-card, .reward-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.badge-card.earned {
  border-color: #f6ad55;
  background: linear-gradient(135deg, #fff5e6 0%, #fef5e7 100%);
}

.badge-card.available {
  border-color: #cbd5e0;
  background: #f7fafc;
  opacity: 0.7;
}

.badge-card:hover, .reward-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.badge-icon, .reward-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.badge-name, .reward-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.badge-date, .badge-requirement, .reward-status {
  font-size: 0.875rem;
  color: #718096;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #4299e1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Leaderboard Modal */
.leaderboard-modal .modal-content {
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.leaderboard-tabs {
  display: flex;
  border-bottom: 2px solid #e2e8f0;
  margin-bottom: 1.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  color: #718096;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn.active {
  color: #4299e1;
  border-bottom-color: #4299e1;
}

.tab-btn:hover {
  color: #4299e1;
  background: #f7fafc;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.leaderboard-item.current-user {
  border-color: #4299e1;
  background: linear-gradient(135deg, #ebf8ff 0%, #f0f9ff 100%);
}

.leaderboard-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.leaderboard-item .rank {
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  min-width: 3rem;
  text-align: center;
}

.leaderboard-item .user-info {
  flex: 1;
}

.leaderboard-item .username {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.leaderboard-item .user-stats {
  font-size: 0.875rem;
  color: #718096;
}

.leaderboard-item .points {
  font-size: 1.25rem;
  font-weight: bold;
  color: #4299e1;
}

/* Friends Section */
.friends-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.add-friend {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.add-friend input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
}

.friends-list, .friend-requests {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
}

.friends-list h4, .friend-requests h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.friend-item, .friend-request {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.friend-avatar {
  font-size: 2rem;
  margin-right: 1rem;
}

.friend-info {
  flex: 1;
}

.friend-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.friend-status {
  font-size: 0.875rem;
  color: #718096;
}

.friend-actions {
  display: flex;
  gap: 0.5rem;
}

.friend-badges {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.weekly-points {
  background: #48bb78;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.no-friends, .no-requests {
  text-align: center;
  color: #718096;
  font-style: italic;
  padding: 2rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }

  .btn {
    border: 2px solid #000;
  }
}
