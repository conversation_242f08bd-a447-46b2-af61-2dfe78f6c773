// Content Manager - Handles AI content curation and session management
class ContentManager {
  constructor() {
    this.baseUrl = '/api/content';
    this.cachedContent = null;
    this.lastRefresh = null;
  }

  async getCuratedContent(forceRefresh = false) {
    try {
      // Check for cached content in localStorage first (unless force refresh)
      if (!forceRefresh) {
        const cachedData = this.getCachedContentFromStorage();
        if (cachedData) {
          this.cachedContent = cachedData;
          return cachedData;
        }
      }

      const token = localStorage.getItem('brainripe_token');
      const url = `${this.baseUrl}/curate${forceRefresh ? '?forceRefresh=true' : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get curated content');
      }

      const data = await response.json();

      // Cache the content both in memory and localStorage
      this.cachedContent = data.content;
      this.lastRefresh = new Date();
      this.saveCachedContentToStorage(data.content);

      return data.content;
    } catch (error) {
      console.error('Get curated content error:', error);

      // Return cached content if available (memory first, then localStorage)
      if (this.cachedContent) {
        return this.cachedContent;
      }

      const cachedData = this.getCachedContentFromStorage();
      if (cachedData) {
        return cachedData;
      }

      // Return fallback content
      return this.getFallbackContent();
    }
  }

  getCachedContentFromStorage() {
    try {
      const cached = localStorage.getItem('brainripe_cached_content');
      if (!cached) return null;

      const parsedCache = JSON.parse(cached);
      const cacheAge = Date.now() - new Date(parsedCache.timestamp).getTime();

      // Only return cached content if it's less than 7 days old (unless explicitly refreshed)
      if (cacheAge < 7 * 24 * 60 * 60 * 1000) {
        return parsedCache.content;
      }

      // Remove expired cache
      localStorage.removeItem('brainripe_cached_content');
      return null;
    } catch (error) {
      console.error('Error reading cached content:', error);
      return null;
    }
  }

  saveCachedContentToStorage(content) {
    try {
      const cacheData = {
        content,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem('brainripe_cached_content', JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error saving cached content:', error);
    }
  }

  clearCachedContent() {
    this.cachedContent = null;
    this.lastRefresh = null;
    localStorage.removeItem('brainripe_cached_content');
  }

  async startSession(contentItem) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/start-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          contentUrl: contentItem.url,
          contentTitle: contentItem.title,
          contentType: contentItem.type,
          contentSource: contentItem.source,
          estimatedDuration: contentItem.estimatedDuration,
          difficultyLevel: contentItem.difficultyLevel,
          content: contentItem.description // Fallback content for chunking
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to start session');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Start session error:', error);
      throw error;
    }
  }

  async getActiveSessions() {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/active-sessions`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get active sessions');
      }

      const data = await response.json();
      return data.sessions || [];
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  }

  async getCompletedSessions(limit = 10) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/completed-sessions?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get completed sessions');
      }

      const data = await response.json();
      return data.sessions || [];
    } catch (error) {
      console.error('Get completed sessions error:', error);
      return [];
    }
  }

  async pauseSession(sessionId) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/pause-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to pause session');
      }

      return await response.json();
    } catch (error) {
      console.error('Pause session error:', error);
      throw error;
    }
  }

  async resumeSession(sessionId) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/resume-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to resume session');
      }

      return await response.json();
    } catch (error) {
      console.error('Resume session error:', error);
      throw error;
    }
  }

  async rateSession(sessionId, rating, feedback = '') {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/rate-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          rating,
          feedback
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to rate session');
      }

      return await response.json();
    } catch (error) {
      console.error('Rate session error:', error);
      throw error;
    }
  }

  // Get fallback content when AI curation fails
  getFallbackContent() {
    return {
      articles: [
        {
          id: 'fallback_article_1',
          title: 'Amazing Animals Around the World',
          url: 'https://kids.nationalgeographic.com/animals/',
          type: 'article',
          source: 'National Geographic Kids',
          estimatedDuration: 5,
          difficultyLevel: 5,
          description: 'Discover incredible facts about animals from around the globe with stunning photos and fun facts.',
          topics: ['animals', 'nature', 'science']
        },
        {
          id: 'fallback_article_2',
          title: 'Space Exploration for Kids',
          url: 'https://www.nasa.gov/audience/forstudents/k-4/',
          type: 'article',
          source: 'NASA Kids',
          estimatedDuration: 7,
          difficultyLevel: 6,
          description: 'Explore space, rockets, and planets through NASA\'s educational resources for kids.',
          topics: ['space', 'science', 'exploration']
        }
      ],
      videos: [
        {
          id: 'fallback_video_1',
          title: 'NASA Kids Educational Content',
          url: 'https://www.nasa.gov/audience/forstudents/k-4/',
          type: 'video',
          source: 'NASA Kids',
          estimatedDuration: 8,
          difficultyLevel: 5,
          description: 'Explore space, rockets, and planets through NASA\'s educational resources for kids.',
          topics: ['space', 'science', 'exploration']
        },
        {
          id: 'fallback_video_2',
          title: 'Smithsonian Kids Learning',
          url: 'https://www.si.edu/kids',
          type: 'video',
          source: 'Smithsonian Kids',
          estimatedDuration: 6,
          difficultyLevel: 5,
          description: 'Discover amazing facts about history, science, and culture from the Smithsonian.',
          topics: ['history', 'science', 'culture']
        }
      ],
      books: [
        {
          id: 'fallback_book_1',
          title: 'The Adventures of Tom Sawyer',
          url: 'https://www.gutenberg.org/ebooks/74',
          type: 'book',
          source: 'Project Gutenberg',
          estimatedDuration: 20,
          difficultyLevel: 7,
          description: 'Join Tom Sawyer on his exciting adventures along the Mississippi River!',
          topics: ['adventure', 'friendship', 'classic']
        },
        {
          id: 'fallback_book_2',
          title: 'Alice\'s Adventures in Wonderland',
          url: 'https://www.gutenberg.org/ebooks/11',
          type: 'book',
          source: 'Project Gutenberg',
          estimatedDuration: 15,
          difficultyLevel: 6,
          description: 'Follow Alice down the rabbit hole into a magical world!',
          topics: ['fantasy', 'adventure', 'imagination']
        }
      ],
      generatedAt: new Date(),
      isFallback: true
    };
  }

  // Filter content by type
  filterContentByType(content, type) {
    if (!content) return [];
    
    switch (type) {
      case 'article':
        return content.articles || [];
      case 'video':
        return content.videos || [];
      case 'book':
        return content.books || [];
      default:
        return [];
    }
  }

  // Get content recommendations based on user interests
  getRecommendedContent(content, userInterests = []) {
    if (!content || userInterests.length === 0) return content;

    const allContent = [
      ...(content.articles || []),
      ...(content.videos || []),
      ...(content.books || [])
    ];

    // Score content based on interest matching
    const scoredContent = allContent.map(item => {
      let score = 0;
      userInterests.forEach(interest => {
        if (item.topics.some(topic => 
          topic.toLowerCase().includes(interest.toLowerCase()) ||
          interest.toLowerCase().includes(topic.toLowerCase())
        )) {
          score += 1;
        }
        if (item.title.toLowerCase().includes(interest.toLowerCase()) ||
            item.description.toLowerCase().includes(interest.toLowerCase())) {
          score += 0.5;
        }
      });
      return { ...item, score };
    });

    // Sort by score and return top recommendations
    const recommended = scoredContent
      .sort((a, b) => b.score - a.score)
      .slice(0, 6);

    return {
      ...content,
      recommended
    };
  }

  // Check if content needs refresh
  needsRefresh() {
    if (!this.lastRefresh) return true;
    
    const hoursSinceRefresh = (Date.now() - this.lastRefresh.getTime()) / (1000 * 60 * 60);
    return hoursSinceRefresh >= 24; // Refresh every 24 hours
  }

  // Clear cached content
  clearCache() {
    this.cachedContent = null;
    this.lastRefresh = null;
  }
}
