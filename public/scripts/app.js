// Main Application Controller
console.log('📜 app.js loaded successfully');

class BrainRipeApp {
  constructor() {
    this.currentUser = null;
    this.currentSession = null;

    // Check if all required classes are available
    if (typeof AuthManager === 'undefined') {
      console.error('❌ AuthManager class not found');
      return;
    }
    if (typeof DashboardManager === 'undefined') {
      console.error('❌ DashboardManager class not found');
      return;
    }
    if (typeof ReadingManager === 'undefined') {
      console.error('❌ ReadingManager class not found');
      return;
    }
    if (typeof ContentManager === 'undefined') {
      console.error('❌ ContentManager class not found');
      return;
    }

    this.authManager = new AuthManager();
    this.dashboardManager = new DashboardManager();
    this.readingManager = new ReadingManager();
    this.contentManager = new ContentManager();

    // Add status indicator
    this.updateLoadingStatus('Initializing app...');

    this.init();
  }

  updateLoadingStatus(message) {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      const statusElement = loadingScreen.querySelector('.loading-status') ||
        (() => {
          const status = document.createElement('p');
          status.className = 'loading-status';
          status.style.marginTop = '20px';
          status.style.fontSize = '14px';
          status.style.color = '#666';
          loadingScreen.querySelector('.loading-content').appendChild(status);
          return status;
        })();
      statusElement.textContent = message;
    }
  }

  async init() {
    try {
      console.log('🚀 BrainRipe app initializing...');
      this.updateLoadingStatus('Starting up...');

      // Show loading screen
      console.log('📱 About to show loading screen');
      this.showScreen('loading');
      console.log('📱 Loading screen shown');
      this.updateLoadingStatus('Loading screen displayed');

      // Check for existing authentication
      const token = localStorage.getItem('brainripe_token');
      console.log('🔑 Checking for existing token:', token ? 'Found' : 'Not found');
      this.updateLoadingStatus('Checking authentication...');

      // Temporarily skip token verification for debugging
      if (token && false) { // Disabled for debugging
        console.log('🔍 Verifying token...');
        this.updateLoadingStatus('Verifying token...');
        const isValid = await this.authManager.verifyToken(token);
        console.log('✅ Token verification result:', isValid);

        if (isValid) {
          this.currentUser = isValid.user;
          console.log('👤 User loaded:', this.currentUser.username);
          this.updateLoadingStatus('Loading dashboard...');
          await this.showDashboard();
          return;
        } else {
          console.log('❌ Token invalid, removing...');
          localStorage.removeItem('brainripe_token');
        }
      }

      // Show authentication screen
      console.log('🔐 Showing authentication screen');
      this.updateLoadingStatus('Preparing login screen...');

      setTimeout(() => {
        this.showScreen('auth');
        this.setupEventListeners();
        console.log('✅ App initialization complete');
      }, 500); // Small delay to show the status

      // Fallback: Show auth screen after 3 seconds if still on loading
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen && !loadingScreen.classList.contains('hidden')) {
          console.log('⚠️ Fallback: Forcing auth screen display');
          this.showScreen('auth');
        }
      }, 3000);

    } catch (error) {
      console.error('❌ App initialization error:', error);
      this.updateLoadingStatus('Error: ' + error.message);
      this.showNotification('Failed to initialize app', 'error');
      setTimeout(() => this.showScreen('auth'), 1000);
    }
  }

  setupEventListeners() {
    // Authentication form listeners
    document.getElementById('show-register').addEventListener('click', (e) => {
      e.preventDefault();
      this.showRegisterForm();
    });

    document.getElementById('show-login').addEventListener('click', (e) => {
      e.preventDefault();
      this.showLoginForm();
    });

    document.getElementById('login-form-element').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    document.getElementById('register-form-element').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // Dashboard listeners
    document.getElementById('logout-btn').addEventListener('click', () => {
      this.handleLogout();
    });

    document.getElementById('profile-btn').addEventListener('click', () => {
      this.dashboardManager.showProfile();
    });

    document.getElementById('leaderboard-btn').addEventListener('click', () => {
      this.showLeaderboard();
    });

    document.getElementById('badges-btn').addEventListener('click', () => {
      this.showBadgesAndRewards();
    });

    document.getElementById('completed-tasks-btn').addEventListener('click', () => {
      this.showCompletedTasks();
    });

    document.getElementById('refresh-content').addEventListener('click', () => {
      this.refreshContent();
    });

    document.getElementById('back-to-dashboard').addEventListener('click', () => {
      this.showDashboard();
    });

    document.getElementById('back-to-dashboard-from-completed').addEventListener('click', () => {
      this.showDashboard();
    });

    // Content category listeners
    document.querySelectorAll('.category-card').forEach(card => {
      card.addEventListener('click', () => {
        const contentType = card.dataset.type;
        this.showContentType(contentType);
      });
    });

    // Reading interface listeners
    document.getElementById('complete-chunk').addEventListener('click', () => {
      this.readingManager.completeCurrentChunk();
    });

    document.getElementById('pause-session').addEventListener('click', () => {
      this.readingManager.pauseSession();
    });

    document.getElementById('quit-session').addEventListener('click', () => {
      this.readingManager.quitSession();
    });

    document.getElementById('back-to-hub').addEventListener('click', () => {
      this.showDashboard();
    });
  }

  showScreen(screenName) {
    console.log(`🖥️ Showing screen: ${screenName}`);

    // Hide all screens first
    const allScreens = document.querySelectorAll('.screen');
    console.log(`📱 Found ${allScreens.length} screens to hide`);
    allScreens.forEach(screen => {
      screen.classList.add('hidden');
      console.log(`🙈 Hidden screen: ${screen.id}`);
    });

    // Show the requested screen
    const targetScreen = document.getElementById(`${screenName}-screen`);
    if (targetScreen) {
      targetScreen.classList.remove('hidden');
      console.log(`✅ ${screenName} screen displayed (${targetScreen.id})`);

      // Verify the screen is actually visible
      const isHidden = targetScreen.classList.contains('hidden');
      console.log(`🔍 Screen ${screenName} hidden class: ${isHidden}`);
    } else {
      console.error(`❌ Screen element not found: ${screenName}-screen`);
      console.log('🔍 Available screen elements:');
      document.querySelectorAll('[id$="-screen"]').forEach(el => {
        console.log(`  - ${el.id}`);
      });
    }
  }

  showLoginForm() {
    document.getElementById('login-form').classList.remove('hidden');
    document.getElementById('register-form').classList.add('hidden');
  }

  showRegisterForm() {
    document.getElementById('register-form').classList.remove('hidden');
    document.getElementById('login-form').classList.add('hidden');
  }

  async handleLogin() {
    try {
      const username = document.getElementById('login-username').value.trim();
      const password = document.getElementById('login-password').value;

      if (!username || !password) {
        this.showNotification('Please fill in all fields', 'error');
        return;
      }

      this.showLoading('Logging in...');

      const result = await this.authManager.login(username, password);
      if (result.success) {
        this.currentUser = result.user;
        localStorage.setItem('brainripe_token', result.token);
        this.showNotification(`Welcome back, ${result.user.username}!`, 'success');
        await this.showDashboard();
      } else {
        this.showNotification(result.error || 'Login failed', 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showNotification('Login failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async handleRegister() {
    try {
      const username = document.getElementById('register-username').value.trim();
      const password = document.getElementById('register-password').value;
      const birthdate = document.getElementById('register-birthdate').value;
      
      // Get selected interests
      const interests = Array.from(document.querySelectorAll('.interest-item input:checked'))
        .map(input => input.value);

      if (!username || !password || !birthdate) {
        this.showNotification('Please fill in all required fields', 'error');
        return;
      }

      // Validate age
      const age = this.calculateAge(new Date(birthdate));
      if (age < 6 || age > 16) {
        this.showNotification('Age must be between 6 and 16 years old', 'error');
        return;
      }

      this.showLoading('Creating your account...');

      const result = await this.authManager.register(username, password, birthdate, interests);
      if (result.success) {
        this.currentUser = result.user;
        localStorage.setItem('brainripe_token', result.token);
        this.showNotification(`Welcome to BrainRipe, ${result.user.username}!`, 'success');
        await this.showDashboard();
      } else {
        this.showNotification(result.error || 'Registration failed', 'error');
      }
    } catch (error) {
      console.error('Registration error:', error);
      this.showNotification('Registration failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async showDashboard() {
    try {
      this.showScreen('dashboard');
      
      // Update user stats in header
      this.updateUserStats();
      
      // Load dashboard content
      await this.dashboardManager.loadDashboard(this.currentUser);
      
      // Load curated content
      await this.loadCuratedContent();
      
      // Load active sessions
      await this.loadActiveSessions();
      
    } catch (error) {
      console.error('Dashboard loading error:', error);
      this.showNotification('Failed to load dashboard', 'error');
    }
  }

  updateUserStats() {
    if (!this.currentUser) return;

    document.getElementById('user-points').textContent = this.currentUser.totalPoints || 0;
    document.getElementById('user-level').textContent = this.currentUser.currentLevel || 1;
    document.getElementById('weekly-progress').textContent = 
      `${this.currentUser.weeklyPoints || 0}/${this.currentUser.weeklyGoal || 100}`;
  }

  async loadCuratedContent(forceRefresh = false) {
    try {
      const content = await this.contentManager.getCuratedContent(forceRefresh);
      this.displayCuratedContent(content);
    } catch (error) {
      console.error('Failed to load curated content:', error);
      this.showNotification('Failed to load content', 'error');
    }
  }

  displayCuratedContent(content) {
    // Update category counts
    document.getElementById('article-count').textContent = 
      `${content.articles?.length || 0} available`;
    document.getElementById('video-count').textContent = 
      `${content.videos?.length || 0} available`;
    document.getElementById('book-count').textContent = 
      `${content.books?.length || 0} available`;

    // Display content items
    const contentList = document.getElementById('content-list');
    contentList.innerHTML = '';

    // Combine all content types
    const allContent = [
      ...(content.articles || []),
      ...(content.videos || []),
      ...(content.books || [])
    ];

    allContent.forEach(item => {
      const contentCard = this.createContentCard(item);
      contentList.appendChild(contentCard);
    });
  }

  createContentCard(item) {
    const card = document.createElement('div');
    card.className = 'content-card';
    card.dataset.contentId = item.id;
    card.dataset.contentType = item.type;

    const typeIcons = {
      article: '📰',
      video: '🎥',
      book: '📚'
    };

    card.innerHTML = `
      <div class="content-card-header">
        <span class="content-type-icon">${typeIcons[item.type]}</span>
        <span class="content-difficulty">Level ${item.difficultyLevel}</span>
      </div>
      <h4 class="content-title">${item.title}</h4>
      <p class="content-description">${item.description}</p>
      <div class="content-meta">
        <span class="content-duration">⏱️ ${item.estimatedDuration}min</span>
        <span class="content-source">📍 ${item.source || 'Unknown Source'}</span>
      </div>
      <div class="content-topics">
        ${item.topics ? item.topics.map(topic => `<span class="topic-tag">${topic}</span>`).join('') : ''}
      </div>
      <div class="content-actions">
        <button class="btn btn-primary start-content-btn">
          Start ${item.type === 'video' ? 'Watching' : 'Reading'}
          <span class="points-preview">+${this.calculateContentPoints(item)} pts</span>
        </button>
      </div>
    `;

    // Add click listener to start content
    card.querySelector('.start-content-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.startContent(item, e.target);
    });

    return card;
  }

  calculateContentPoints(item) {
    // Base points calculation
    const basePoints = 10;
    const difficultyMultiplier = item.difficultyLevel * 0.2;
    const durationBonus = Math.min(item.estimatedDuration / 5, 10);
    
    return Math.round(basePoints * (1 + difficultyMultiplier) + durationBonus);
  }

  async startContent(contentItem, buttonElement = null) {
    try {
      // Prevent double-clicking by disabling the button
      if (buttonElement) {
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = 'Starting... ⏳';
        buttonElement.disabled = true;
      }

      this.showLoading('Preparing your content...');

      const session = await this.contentManager.startSession(contentItem);
      if (session.success) {
        this.currentSession = session.session;

        // Remove this content from the main grid and refresh active sessions
        this.removeContentFromGrid(contentItem);
        await this.loadActiveSessions();

        await this.readingManager.startReading(session.session, session.chunkedContent);
        this.showScreen('reading');
      } else {
        this.showNotification(session.error || 'Failed to start content', 'error');
        // Re-enable button on error
        if (buttonElement) {
          buttonElement.innerHTML = originalText;
          buttonElement.disabled = false;
        }
      }
    } catch (error) {
      console.error('Start content error:', error);
      this.showNotification('Failed to start content', 'error');
      // Re-enable button on error
      if (buttonElement) {
        buttonElement.innerHTML = originalText;
        buttonElement.disabled = false;
      }
    } finally {
      this.hideLoading();
    }
  }

  markContentAsStarted(contentItem) {
    // Find the content card and mark it as started
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
      const titleElement = card.querySelector('.content-title');
      if (titleElement && titleElement.textContent === contentItem.title) {
        card.classList.add('content-started');
        const startBtn = card.querySelector('.start-content-btn');
        if (startBtn) {
          startBtn.innerHTML = '✓ Started';
          startBtn.disabled = true;
          startBtn.classList.add('btn-disabled');
        }
      }
    });
  }

  removeContentFromGrid(contentItem) {
    // Find and remove the content card from the grid
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
      const titleElement = card.querySelector('.content-title');
      if (titleElement && titleElement.textContent === contentItem.title) {
        card.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        card.style.opacity = '0';
        card.style.transform = 'scale(0.9)';
        setTimeout(() => {
          card.remove();
        }, 300);
      }
    });
  }

  async loadActiveSessions() {
    try {
      const sessions = await this.contentManager.getActiveSessions();
      this.displayActiveSessions(sessions);
    } catch (error) {
      console.error('Failed to load active sessions:', error);
    }
  }

  displayActiveSessions(sessions) {
    const sessionsList = document.getElementById('active-sessions-list');
    sessionsList.innerHTML = '';

    if (!sessions || sessions.length === 0) {
      sessionsList.innerHTML = '<p class="no-sessions">No active sessions. Start reading something new!</p>';
      return;
    }

    sessions.forEach(session => {
      const sessionCard = this.createSessionCard(session);
      sessionsList.appendChild(sessionCard);
    });
  }

  createSessionCard(session) {
    const card = document.createElement('div');
    card.className = 'session-card';
    
    const typeIcons = {
      article: '📰',
      video: '🎥',
      book: '📚'
    };

    card.innerHTML = `
      <div class="session-icon">${typeIcons[session.contentType]}</div>
      <div class="session-info">
        <h4>${session.contentTitle}</h4>
        <div class="session-progress">
          <div class="progress-bar small">
            <div class="progress-fill" style="width: ${session.completionPercentage}%"></div>
          </div>
          <span>${session.completedChunks}/${session.totalChunks} chunks</span>
        </div>
      </div>
      <button class="btn btn-secondary continue-btn">Continue</button>
    `;

    card.querySelector('.continue-btn').addEventListener('click', () => {
      this.continueSession(session);
    });

    return card;
  }

  async continueSession(session) {
    try {
      this.showLoading('Loading your session...');
      this.currentSession = session;
      await this.readingManager.continueReading(session);
      this.showScreen('reading');
    } catch (error) {
      console.error('Continue session error:', error);
      this.showNotification('Failed to continue session', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async refreshContent() {
    try {
      this.showLoading('Getting fresh content...');
      const content = await this.contentManager.getCuratedContent(true);
      this.displayCuratedContent(content);
      this.showNotification('Content refreshed!', 'success');
    } catch (error) {
      console.error('Refresh content error:', error);
      this.showNotification('Failed to refresh content', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async showCompletedTasks() {
    try {
      this.showLoading('Loading completed tasks...');
      const completedSessions = await this.contentManager.getCompletedSessions(50);
      this.displayCompletedTasks(completedSessions);
      this.showScreen('completed-tasks');
    } catch (error) {
      console.error('Show completed tasks error:', error);
      this.showNotification('Failed to load completed tasks', 'error');
    } finally {
      this.hideLoading();
    }
  }

  displayCompletedTasks(sessions) {
    const completedTasksList = document.getElementById('completed-tasks-list');
    const totalCompletedEl = document.getElementById('total-completed');

    if (!sessions || sessions.length === 0) {
      completedTasksList.innerHTML = `
        <div class="no-completed-tasks">
          <div class="empty-state">
            <div class="empty-icon">📚</div>
            <h3>No completed tasks yet</h3>
            <p>Complete some reading sessions to see them here!</p>
          </div>
        </div>
      `;
      totalCompletedEl.textContent = '0 completed';
      return;
    }

    totalCompletedEl.textContent = `${sessions.length} completed`;

    const tasksHTML = sessions.map(session => {
      const completedDate = new Date(session.completedAt).toLocaleDateString();
      const typeIcon = {
        article: '📰',
        video: '🎥',
        book: '📚'
      }[session.contentType] || '📄';

      return `
        <div class="completed-task-card" data-type="${session.contentType}">
          <div class="task-icon">${typeIcon}</div>
          <div class="task-info">
            <h4 class="task-title">${session.contentTitle}</h4>
            <div class="task-meta">
              <span class="task-date">Completed: ${completedDate}</span>
              <span class="task-points">+${session.pointsEarned} points</span>
              <span class="task-time">${Math.floor(session.totalTimeSpent / 60)}min</span>
            </div>
          </div>
          <div class="task-stats">
            <div class="completion-badge">✓ Complete</div>
          </div>
        </div>
      `;
    }).join('');

    completedTasksList.innerHTML = tasksHTML;

    // Add filter functionality
    this.setupCompletedTasksFilters(sessions);
  }

  setupCompletedTasksFilters(sessions) {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const completedTasksList = document.getElementById('completed-tasks-list');

    filterButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        // Update active filter
        filterButtons.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        const filter = btn.dataset.filter;
        const filteredSessions = filter === 'all'
          ? sessions
          : sessions.filter(session => session.contentType === filter);

        // Re-render with filtered sessions
        this.displayCompletedTasks(filteredSessions);
      });
    });
  }

  handleLogout() {
    localStorage.removeItem('brainripe_token');
    this.contentManager.clearCachedContent(); // Clear cached content on logout
    this.currentUser = null;
    this.currentSession = null;
    this.showScreen('auth');
    this.showNotification('Logged out successfully', 'success');
  }

  showLoading(message = 'Loading...') {
    // You can implement a loading overlay here
    console.log('Loading:', message);
  }

  hideLoading() {
    // Hide loading overlay
    console.log('Loading complete');
  }

  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <span class="notification-icon">${icons[type]}</span>
      <span class="notification-message">${message}</span>
      <button class="notification-close">×</button>
    `;

    // Add close functionality
    notification.querySelector('.notification-close').addEventListener('click', () => {
      notification.remove();
    });

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }

  showContentType(contentType) {
    // Filter content list to show only selected type
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
      if (card.dataset.contentType === contentType) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });

    // Update category selection visual feedback
    document.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('selected');
    });
    document.querySelector(`[data-type="${contentType}"]`).classList.add('selected');
  }

  showBadgesAndRewards() {
    // Create and show badges and rewards modal
    const modal = document.createElement('div');
    modal.className = 'modal badges-rewards-modal';

    const badges = this.currentUser.badges || [];
    const availableBadges = this.getAvailableBadges();

    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>🏅 Badges & Rewards</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <!-- Earned Badges Section -->
          <div class="rewards-section">
            <h4>Your Badges (${badges.length})</h4>
            <div class="badges-grid">
              ${badges.length > 0 ? badges.map(badge => `
                <div class="badge-card earned">
                  <div class="badge-icon">🏅</div>
                  <div class="badge-name">${badge.name}</div>
                  <div class="badge-date">Earned: ${new Date(badge.earnedAt).toLocaleDateString()}</div>
                </div>
              `).join('') : '<div class="empty-state"><p>No badges earned yet. Keep learning to earn your first badge!</p></div>'}
            </div>
          </div>

          <!-- Available Badges Section -->
          <div class="rewards-section">
            <h4>Available Badges</h4>
            <div class="badges-grid">
              ${availableBadges.map(badge => `
                <div class="badge-card available">
                  <div class="badge-icon">🔒</div>
                  <div class="badge-name">${badge.name}</div>
                  <div class="badge-requirement">${badge.requirement}</div>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Parent Rewards Section -->
          <div class="rewards-section">
            <h4>Parent Rewards</h4>
            <div class="parent-rewards">
              ${this.currentUser.hasParentAccount ?
                `<div class="rewards-grid">
                  <div class="reward-card">
                    <div class="reward-icon">🎁</div>
                    <div class="reward-name">Extra Screen Time</div>
                    <div class="reward-status">Available</div>
                  </div>
                  <div class="reward-card">
                    <div class="reward-icon">🍕</div>
                    <div class="reward-name">Pizza Night</div>
                    <div class="reward-status">Earned</div>
                  </div>
                </div>` :
                `<div class="empty-state">
                  <p>Connect with a parent account to see custom rewards!</p>
                  <button class="btn btn-primary" onclick="window.brainRipeApp.dashboardManager.createParentAccount(); this.closest('.modal').remove();">Create Parent Account</button>
                </div>`
              }
            </div>
          </div>

          <!-- Progress Stats -->
          <div class="rewards-section">
            <h4>Your Progress</h4>
            <div class="progress-stats">
              <div class="stat-item">
                <div class="stat-value">${this.currentUser.totalPoints || 0}</div>
                <div class="stat-label">Total Points</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${this.currentUser.currentLevel || 1}</div>
                <div class="stat-label">Current Level</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${badges.length}</div>
                <div class="stat-label">Badges Earned</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" onclick="this.closest('.modal').remove()">Close</button>
        </div>
      </div>
    `;

    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });

    document.body.appendChild(modal);
  }

  getAvailableBadges() {
    const earnedBadgeIds = (this.currentUser.badges || []).map(b => b.badgeId);

    const allBadges = [
      { id: 'first_completion', name: 'First Steps', requirement: 'Complete your first reading session' },
      { id: 'news_reader', name: 'News Explorer', requirement: 'Complete 5 news articles' },
      { id: 'video_watcher', name: 'Video Scholar', requirement: 'Complete 5 educational videos' },
      { id: 'book_reader', name: 'Book Lover', requirement: 'Complete 5 books or stories' },
      { id: 'point_collector_100', name: 'Point Collector', requirement: 'Earn 100 total points' },
      { id: 'point_collector_500', name: 'Point Master', requirement: 'Earn 500 total points' },
      { id: 'point_collector_1000', name: 'Point Champion', requirement: 'Earn 1000 total points' },
      { id: 'streak_reader', name: 'Consistent Reader', requirement: 'Read for 7 days in a row' },
      { id: 'speed_reader', name: 'Speed Reader', requirement: 'Complete 10 sessions quickly' },
      { id: 'curious_mind', name: 'Curious Mind', requirement: 'Try all content types' }
    ];

    return allBadges.filter(badge => !earnedBadgeIds.includes(badge.id));
  }

  showLeaderboard() {
    // Create and show leaderboard modal
    const modal = document.createElement('div');
    modal.className = 'modal leaderboard-modal';

    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>🏆 Leaderboard & Friends</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <!-- Leaderboard Tabs -->
          <div class="leaderboard-tabs">
            <button class="tab-btn active" data-tab="weekly">Weekly</button>
            <button class="tab-btn" data-tab="monthly">Monthly</button>
            <button class="tab-btn" data-tab="friends">Friends</button>
          </div>

          <!-- Weekly Leaderboard -->
          <div class="tab-content active" id="weekly-tab">
            <div class="leaderboard-list">
              <div class="leaderboard-item current-user">
                <div class="rank">🥇</div>
                <div class="user-info">
                  <div class="username">${this.currentUser.username} (You)</div>
                  <div class="user-stats">${this.currentUser.weeklyPoints || 0} points this week</div>
                </div>
                <div class="points">${this.currentUser.weeklyPoints || 0}</div>
              </div>
              <div class="leaderboard-item">
                <div class="rank">🥈</div>
                <div class="user-info">
                  <div class="username">Alex_Reader</div>
                  <div class="user-stats">245 points this week</div>
                </div>
                <div class="points">245</div>
              </div>
              <div class="leaderboard-item">
                <div class="rank">🥉</div>
                <div class="user-info">
                  <div class="username">BookWorm_Sam</div>
                  <div class="user-stats">198 points this week</div>
                </div>
                <div class="points">198</div>
              </div>
              <div class="leaderboard-item">
                <div class="rank">4</div>
                <div class="user-info">
                  <div class="username">CuriousKid</div>
                  <div class="user-stats">156 points this week</div>
                </div>
                <div class="points">156</div>
              </div>
            </div>
          </div>

          <!-- Monthly Leaderboard -->
          <div class="tab-content" id="monthly-tab">
            <div class="leaderboard-list">
              <div class="leaderboard-item">
                <div class="rank">🥇</div>
                <div class="user-info">
                  <div class="username">SuperReader_Max</div>
                  <div class="user-stats">1,245 points this month</div>
                </div>
                <div class="points">1,245</div>
              </div>
              <div class="leaderboard-item current-user">
                <div class="rank">2</div>
                <div class="user-info">
                  <div class="username">${this.currentUser.username} (You)</div>
                  <div class="user-stats">${this.currentUser.totalPoints || 0} points this month</div>
                </div>
                <div class="points">${this.currentUser.totalPoints || 0}</div>
              </div>
              <div class="leaderboard-item">
                <div class="rank">3</div>
                <div class="user-info">
                  <div class="username">BookWorm_Sam</div>
                  <div class="user-stats">892 points this month</div>
                </div>
                <div class="points">892</div>
              </div>
            </div>
          </div>

          <!-- Friends Tab -->
          <div class="tab-content" id="friends-tab">
            <div class="friends-section">
              <div class="add-friend">
                <input type="text" placeholder="Enter friend's username" id="friend-username">
                <button class="btn btn-primary" id="add-friend-btn">Add Friend</button>
              </div>

              <div class="friends-list">
                <h4 id="friends-count">Your Friends (0)</h4>
                <div id="friends-container">
                  <p class="no-friends">No friends yet. Add some friends to see them here!</p>
                </div>
              </div>

              <div class="friend-requests">
                <h4 id="requests-count">Friend Requests (0)</h4>
                <div id="friend-requests-container">
                  <p class="no-requests">No pending friend requests.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" onclick="this.closest('.modal').remove()">Close</button>
        </div>
      </div>
    `;

    // Add tab switching functionality
    modal.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        // Remove active class from all tabs and content
        modal.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        modal.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        // Add active class to clicked tab and corresponding content
        btn.classList.add('active');
        modal.querySelector(`#${btn.dataset.tab}-tab`).classList.add('active');
      });
    });

    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });

    // Set up add friend button listener
    modal.querySelector('#add-friend-btn').addEventListener('click', () => {
      this.addFriend();
    });

    // Set up tab switching to load friends data when friends tab is clicked
    modal.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        if (btn.dataset.tab === 'friends') {
          this.loadFriendsData();
        }
      });
    });

    document.body.appendChild(modal);

    // Load friends data immediately if friends tab is active
    this.loadFriendsData();
  }

  async addFriend() {
    const username = document.getElementById('friend-username').value.trim();
    if (!username) {
      this.showNotification('Please enter a username', 'error');
      return;
    }

    try {
      const response = await fetch('/api/social/friend-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ targetUsername: username })
      });

      const data = await response.json();

      if (response.ok) {
        this.showNotification(`Friend request sent to ${username}!`, 'success');
        document.getElementById('friend-username').value = '';
      } else {
        this.showNotification(data.error || 'Failed to send friend request', 'error');
      }
    } catch (error) {
      console.error('Add friend error:', error);
      this.showNotification('Network error. Please try again.', 'error');
    }
  }

  async loadFriendsData() {
    try {
      // Load friends list
      const friendsResponse = await fetch('/api/social/friends', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (friendsResponse.ok) {
        const friendsData = await friendsResponse.json();
        this.displayFriends(friendsData.friends);
      }

      // Load friend requests
      const requestsResponse = await fetch('/api/social/friend-requests', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (requestsResponse.ok) {
        const requestsData = await requestsResponse.json();
        this.displayFriendRequests(requestsData.requests);
      }

    } catch (error) {
      console.error('Load friends data error:', error);
    }
  }

  displayFriends(friends) {
    const friendsContainer = document.getElementById('friends-container');
    const friendsCount = document.getElementById('friends-count');

    if (!friendsContainer || !friendsCount) return;

    friendsCount.textContent = `Your Friends (${friends.length})`;

    if (friends.length === 0) {
      friendsContainer.innerHTML = '<p class="no-friends">No friends yet. Add some friends to see them here!</p>';
      return;
    }

    friendsContainer.innerHTML = friends.map(friend => `
      <div class="friend-item">
        <div class="friend-avatar">👤</div>
        <div class="friend-info">
          <div class="friend-name">${friend.username}</div>
          <div class="friend-status">Level ${friend.currentLevel} • ${friend.totalPoints} pts</div>
        </div>
        <div class="friend-badges">
          ${friend.weeklyPoints ? `<span class="weekly-points">+${friend.weeklyPoints} this week</span>` : ''}
        </div>
      </div>
    `).join('');
  }

  displayFriendRequests(requests) {
    const requestsContainer = document.getElementById('friend-requests-container');
    const requestsCount = document.getElementById('requests-count');

    if (!requestsContainer || !requestsCount) return;

    requestsCount.textContent = `Friend Requests (${requests.length})`;

    if (requests.length === 0) {
      requestsContainer.innerHTML = '<p class="no-requests">No pending friend requests.</p>';
      return;
    }

    requestsContainer.innerHTML = requests.map(request => `
      <div class="friend-request" data-request-id="${request.id}">
        <div class="friend-avatar">👤</div>
        <div class="friend-info">
          <div class="friend-name">${request.requester.username}</div>
          <div class="friend-status">Level ${request.requester.currentLevel} • Wants to be friends</div>
        </div>
        <div class="friend-actions">
          <button class="btn btn-primary btn-small accept-request" data-request-id="${request.id}">Accept</button>
          <button class="btn btn-secondary btn-small decline-request" data-request-id="${request.id}">Decline</button>
        </div>
      </div>
    `).join('');

    // Add event listeners for accept/decline buttons
    requestsContainer.querySelectorAll('.accept-request').forEach(btn => {
      btn.addEventListener('click', () => this.respondToFriendRequest(btn.dataset.requestId, 'accept'));
    });

    requestsContainer.querySelectorAll('.decline-request').forEach(btn => {
      btn.addEventListener('click', () => this.respondToFriendRequest(btn.dataset.requestId, 'decline'));
    });
  }

  async respondToFriendRequest(requestId, action) {
    try {
      const response = await fetch(`/api/social/friend-request/${requestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ action })
      });

      const data = await response.json();

      if (response.ok) {
        this.showNotification(
          action === 'accept' ? 'Friend request accepted!' : 'Friend request declined',
          'success'
        );
        // Reload friends data to update the display
        this.loadFriendsData();
      } else {
        this.showNotification(data.error || 'Failed to respond to friend request', 'error');
      }
    } catch (error) {
      console.error('Respond to friend request error:', error);
      this.showNotification('Network error. Please try again.', 'error');
    }
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🌟 DOM loaded, initializing BrainRipe app...');

  // Set up manual continue button as fallback
  setTimeout(() => {
    const manualButton = document.getElementById('manual-continue');
    if (manualButton) {
      manualButton.style.display = 'block';
      manualButton.addEventListener('click', () => {
        if (window.brainRipeApp) {
          window.brainRipeApp.showScreen('auth');
        }
      });
    }
  }, 5000);

  // Set up debug button
  const debugButton = document.getElementById('debug-screens');
  if (debugButton) {
    debugButton.addEventListener('click', () => {
      console.log('🔍 Debug: All screens:');
      document.querySelectorAll('.screen').forEach(screen => {
        const isHidden = screen.classList.contains('hidden');
        const computedStyle = window.getComputedStyle(screen);
        const displayValue = computedStyle.display;
        console.log(`  - ${screen.id}: hidden=${isHidden}, display=${displayValue}`);
      });

      if (window.brainRipeApp) {
        console.log('🔄 Testing screen switch to auth...');
        window.brainRipeApp.showScreen('auth');
      }
    });
  }

  try {
    window.brainRipeApp = new BrainRipeApp();
    console.log('🎯 BrainRipe app instance created');
  } catch (error) {
    console.error('❌ Failed to create app instance:', error);
  }
});
