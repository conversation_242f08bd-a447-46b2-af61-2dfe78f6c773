{"name": "brainripe", "version": "1.0.0", "description": "A gamified web application that motivates children to engage with long-form educational content", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["education", "gamification", "children", "reading", "ai"], "author": "BrainRipe Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "node-cron": "^4.2.1", "openai": "^4.20.1", "validator": "^13.11.0"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^10.2.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}