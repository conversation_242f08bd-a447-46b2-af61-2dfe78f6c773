# BrainRipe 🧠🍎

A gamified web application that motivates children (ages 8-14) to engage with long-form educational content through chunked reading/viewing, progress tracking, and reward systems.

## Complete Setup Guide

### 0. Starting after shutting down VSCode

If you have already completed the setup and just need to start the application after shutting down VSCode, you can follow these steps:

```bash
# 1. Start MongoDB (if not running)
sudo docker start brainripe-mongo

# 2. Navigate to project
cd /path/to/brainripe

# 3. Start development server
npm run dev
```

### 1. <PERSON><PERSON> and Install

```bash
# Clone the repository
git clone <repository-url>
cd brainripe

# Install all dependencies
npm install

# If you encounter missing dependencies, install them individually:
npm install express mongoose dotenv jsonwebtoken bcryptjs cors helmet express-rate-limit node-cron
npm install --save-dev nodemon
```

### 2. Set up MongoDB with Docker

**Install Docker (Ubuntu/WSL):**
```bash
sudo apt update
sudo apt install docker.io
sudo systemctl start docker
sudo systemctl enable docker
```

**Run MongoDB container:**
```bash
# Create and start MongoDB container
sudo docker run -d --name brainripe-mongo -p 27017:27017 mongo:6.0

# Verify it's running
sudo docker ps
```

### 3. Get OpenAI API Key

1. **Sign up at [platform.openai.com](https://platform.openai.com)**
2. **Navigate to [API Keys](https://platform.openai.com/api-keys)**
3. **Click "Create new secret key"**
4. **Copy the key** (starts with `sk-proj-...`)
5. **Set spending limits** at [Usage page](https://platform.openai.com/usage) (recommended: $10-25/month)

### 4. Configure Environment Variables

**Create `.env` file:**
```bash
cp .env.example .env
```

**Or create manually with these contents:**
```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/brainripe

# OpenAI Configuration (IMPORTANT: Use gpt-3.5-turbo for cost savings)
OPENAI_API_KEY=sk-proj-your-actual-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=3000
NODE_ENV=development

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Content Curation Settings
CONTENT_REFRESH_INTERVAL_HOURS=24
MAX_CONTENT_ITEMS_PER_TYPE=10
```

**Generate a secure JWT secret:**
```bash
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 5. Start the Application

```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

**You should see:**
```
✅ MongoDB connected successfully
🚀 BrainRipe server running on port 3000
📱 Main app: http://localhost:3000
👨‍👩‍👧‍👦 Parent dashboard: http://localhost:3000/parent
```

### 6. Access Your Application

- **Main app**: http://localhost:3000
- **Parent dashboard**: http://localhost:3000/parent  
- **Health check**: http://localhost:3000/api/health

## Quick Daily Startup

For subsequent development sessions:

```bash
# 1. Start MongoDB (if not running)
sudo docker start brainripe-mongo

# 2. Navigate to project
cd /path/to/brainripe

# 3. Start development server
npm run dev
```

## Features

- **AI-Powered Content Curation**: Personalized educational content recommendations
- **Chunked Learning**: Age-appropriate content segmentation for better comprehension
- **Gamification**: Points, badges, and rewards system
- **Social Features**: Friend system and weekly leaderboards
- **Parent Dashboard**: Progress monitoring and goal setting
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with Express
- **Database**: MongoDB (via Docker)
- **AI Integration**: OpenAI API for content curation
- **Authentication**: JWT tokens

## Prerequisites

- **Node.js** (version 18 or higher)
- **Docker** (for MongoDB)
- **OpenAI API Key** (gpt-3.5-turbo model)

## Project Structure

```
brainripe/
├── public/                 # Frontend files
│   ├── index.html         # Main HTML file
│   ├── styles/            # CSS files
│   ├── scripts/           # JavaScript files
│   └── images/            # Static images
├── server/                # Backend files
│   ├── server.js          # Main server file
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── middleware/        # Custom middleware
│   └── utils/             # Utility functions
├── tests/                 # Test files
├── package.json           # Dependencies and scripts
├── .env                   # Environment variables (create this)
└── README.md             # This file
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/parent-setup` - Parent account setup

### Content
- `GET /api/content/curate` - Get curated content
- `POST /api/content/start-session` - Start reading session
- `POST /api/content/complete-chunk/:sessionId` - Complete chunk

### Progress
- `GET /api/progress/stats` - Get user statistics
- `POST /api/progress/add-points` - Add points to user

### Social
- `GET /api/social/leaderboard` - Weekly leaderboard
- `POST /api/social/friend-request` - Send friend request
- `POST /api/social/accept-friend/:requesterId` - Accept friend request

## Troubleshooting

### Common Issues

**MongoDB connection fails:**
```bash
# Check if Docker container is running
sudo docker ps

# Start the container if stopped
sudo docker start brainripe-mongo

# If container doesn't exist, recreate it
sudo docker run -d --name brainripe-mongo -p 27017:27017 mongo:6.0
```

**Missing dependencies:**
```bash
# Install missing packages as they appear
npm install [package-name]

# Common missing packages:
npm install node-cron multer express-validator cookie-parser
```

**OpenAI API errors:**
- Verify your API key is correct in `.env`
- Check spending limits at [platform.openai.com](https://platform.openai.com/usage)
- Ensure you're using `gpt-3.5-turbo` (much cheaper than GPT-4)

**Port 3000 already in use:**
```bash
# Find what's using the port
sudo lsof -i :3000

# Kill the process or change PORT in .env file
```

**Permission errors with Docker:**
```bash
# Add your user to docker group
sudo usermod -aG docker $USER
# Log out and back in, then try docker commands without sudo
```

### Security Vulnerabilities

If `npm install` shows security vulnerabilities:
```bash
# Try safe fixes first
npm audit fix

# Check what remains
npm audit

# Update packages
npm update
```

## Cost Optimization

**OpenAI API costs:**
- Use `gpt-3.5-turbo` instead of `gpt-4` (20x cheaper)
- Set spending limits in OpenAI dashboard
- Cache AI responses when possible
- Reduce `max_tokens` in API calls

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

MIT License - see LICENSE file for details

---

**Need help?** Check the troubleshooting section above or open an issue in the repository.