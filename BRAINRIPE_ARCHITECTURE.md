# BrainRipe App Architecture & Page Structure

## Overview
BrainRipe is an educational app for children that provides AI-curated content (articles, videos, books) with chunked reading experiences, social features, and parent controls.

## Core Architecture

### Frontend Structure
```
public/
├── index.html          # Main app entry point
├── parent.html         # Parent dashboard interface
├── scripts/
│   ├── app.js          # Main app controller & screen management
│   ├── auth.js         # Authentication & user management
│   ├── dashboard.js    # Main dashboard functionality
│   ├── reading.js      # Reading interface & chunk management
│   ├── ai-content.js   # Content fetching & caching
│   ├── parent-app.js   # Parent dashboard controller
│   ├── parent-auth.js  # Parent authentication
│   └── parent-dashboard.js # Parent dashboard management
└── styles/
    ├── main.css        # Global styles
    ├── dashboard.css   # Dashboard-specific styles
    ├── reading.css     # Reading interface styles
    └── parent.css      # Parent dashboard styles
```

### Backend Structure
```
server/
├── routes/
│   ├── auth.js         # User & parent authentication
│   ├── content.js      # Content curation & session management
│   └── social.js       # Friends & social features
├── models/
│   ├── User.js         # User data model
│   ├── ParentAccount.js # Parent account model
│   ├── ContentSession.js # Reading session tracking
│   ├── Friendship.js   # Friend relationships
│   └── WeeklyLeaderboard.js # Leaderboard system
├── utils/
│   └── ai-integration.js # OpenAI content curation
└── middleware/
    └── auth.js         # Authentication middleware
```

## Page Flow & Functionality

### 1. Main App (index.html)
**Entry Point**: `public/index.html`
**Controller**: `scripts/app.js` (BrainRipeApp class)

#### Screens:
- **Loading Screen**: Initial app loading
- **Auth Screen**: Login/registration
- **Dashboard Screen**: Main content hub
- **Reading Screen**: Chunked content reading interface

#### Key Features:
- Screen management system
- Content discovery and categorization
- Active session management
- User profile and settings

### 2. Dashboard Functionality
**Controller**: `scripts/dashboard.js` (DashboardManager class)

#### Sections:
- **Content Hub**: Browse articles, videos, books
- **Active Sessions**: Continue reading in-progress content
- **Profile Management**: User settings, interests, badges
- **Parent Account Integration**: Connect/manage parent controls

#### Content Flow:
1. User selects content type (article/video/book)
2. Content filtered and displayed from AI curation
3. User clicks "Start Activity" → creates session → goes to reading interface

### 3. Reading Interface
**Controller**: `scripts/reading.js` (ReadingManager class)

#### Features:
- **Chunked Content**: Content broken into digestible pieces
- **Progress Tracking**: Visual progress bar and completion stats
- **Source Links**: Links to original content (CURRENTLY BROKEN)
- **Points System**: Earn points for completing chunks
- **Session Management**: Pause/resume functionality

#### Current Issues:
- Source links not properly populated
- "View Original" link not prominently displayed
- Links may be broken/invalid

### 4. Parent Dashboard (parent.html)
**Entry Point**: `public/parent.html`
**Controller**: `scripts/parent-app.js` (ParentApp class)

#### Features:
- **Child Progress Monitoring**: View reading stats and achievements
- **Content Restrictions**: Block topics, set screen time limits
- **Goal Setting**: Weekly/monthly reading goals
- **Social Controls**: Enable/disable friend features

#### Current Issues:
- CSP violations with inline event handlers
- Access issues due to security policy

### 5. Social Features
**Backend**: `server/routes/social.js`
**Models**: `server/models/Friendship.js`

#### Features:
- **Friend Search**: Search users by username
- **Friend Requests**: Send/accept/decline requests
- **Activity Feed**: See friends' reading activity
- **Leaderboards**: Weekly point competitions

#### Current Issues:
- May have fake/mock data instead of real user search
- Need to implement proper user search functionality

## Data Flow

### Content Curation Process:
1. **AI Integration** (`server/utils/ai-integration.js`):
   - Uses OpenAI to generate age-appropriate content recommendations
   - Validates content items for safety and appropriateness
   - Falls back to hardcoded content if AI fails

2. **Content Serving** (`server/routes/content.js`):
   - Applies parent restrictions and filters
   - Caches content for performance
   - Tracks user sessions and progress

3. **Frontend Display** (`scripts/ai-content.js`):
   - Fetches and caches content locally
   - Handles content categorization and filtering
   - Manages session state

### Authentication Flow:
1. **User Auth**: Standard JWT-based authentication
2. **Parent Auth**: Separate parent account system with email verification
3. **Session Management**: Tokens stored in localStorage
4. **Middleware**: Server-side auth validation for all protected routes

## Critical Issues - Status Update

### ✅ 1. URL Validation (COMPLETED)
**Problem**: AI-generated content URLs were invalid/broken causing 404 errors
**Solution**: Implemented comprehensive URL validation system
- Added `verifyUrlAccessibility()` method in `server/utils/ai-integration.js`
- HTTP HEAD requests with 10-second timeout to verify links
- Enhanced fallback content with verified educational URLs
- **Files Modified**: `server/utils/ai-integration.js`, `public/scripts/ai-content.js`

### ✅ 2. Content Security Policy Violations (COMPLETED)
**Problem**: Inline event handlers (`onclick`) violated CSP blocking parent dashboard
**Solution**: Replaced all inline handlers with proper event listeners
- Removed inline handlers from dashboard and test screens
- Updated CSP headers to be more restrictive (`script-src-attr: 'none'`)
- **Files Modified**: `public/scripts/dashboard.js`, `public/test-screens.html`, `server/server.js`

### ✅ 3. Content Source Display (COMPLETED)
**Problem**: Source information not prominently displayed, "View Original" link hidden
**Solution**: Created prominent source link section in reading interface
- Added dedicated source section with clear call-to-action
- Dynamic link text based on content type (article/video/book)
- Better visual hierarchy and user guidance
- **Files Modified**: `public/index.html`, `public/scripts/reading.js`, `public/styles/reading.css`

### ✅ 4. Friends System (COMPLETED)
**Problem**: Fake/mock user data instead of real user search functionality
**Solution**: Integrated real friends API with proper user search
- Replaced hardcoded friends with real API calls to `/api/social/friends`
- Implemented username search via `/api/social/search-users`
- Added friend request system with accept/decline functionality
- Display real user badges, levels, and stats
- **Files Modified**: `public/scripts/app.js`, `public/styles/main.css`

### 🔄 5. Code Organization (IN PROGRESS)
**Goal**: Improve modularity and documentation for easier maintenance
**Progress**: Architecture documentation updated with current system state
- **Files Modified**: `BRAINRIPE_ARCHITECTURE.md`

## All Critical Issues Resolved ✅
The app now has:
- ✅ Working content links with validation
- ✅ Secure CSP compliance without inline handlers
- ✅ Prominent content source display
- ✅ Real friends system with user search
- ✅ Comprehensive architecture documentation
