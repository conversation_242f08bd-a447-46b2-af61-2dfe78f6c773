const OpenAI = require('openai');
const https = require('https');
const http = require('http');
const { URL } = require('url');

class ContentCurator {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.lastContentRefresh = {};
    this.contentCache = {};
    this.refreshIntervalHours = parseInt(process.env.CONTENT_REFRESH_INTERVAL_HOURS) || 24;
    this.urlValidationTimeout = 10000; // 10 seconds timeout for URL validation
  }

  // Main method to get curated content
  async getCuratedContent(userId, userAge, interests = [], forceRefresh = false, userPreferences = {}, userStats = {}) {
    try {
      // Check if content needs refresh
      if (!forceRefresh && !this.shouldRefreshContent(userId)) {
        const cachedContent = this.getCachedContent(userId);
        if (cachedContent) {
          return cachedContent;
        }
      }

      // Calculate progressive difficulty based on user stats and preferences
      const progressiveDifficulty = this.calculateProgressiveDifficulty(userAge, userStats, userPreferences);

      // Generate new content recommendations
      const content = await this.generateContentRecommendations(userAge, interests, progressiveDifficulty, userPreferences);

      // Cache the content
      this.cacheContent(userId, content);
      this.lastContentRefresh[userId] = new Date();

      return content;
    } catch (error) {
      console.error('Content curation error:', error);

      // Return cached content if available, otherwise return fallback
      const cachedContent = this.getCachedContent(userId);
      if (cachedContent) {
        return cachedContent;
      }

      return this.getFallbackContent(userAge);
    }
  }

  // Calculate progressive difficulty based on user progress
  calculateProgressiveDifficulty(userAge, userStats, userPreferences) {
    const { contentDifficulty = 'auto' } = userPreferences;

    // If user has set manual difficulty, use that
    if (contentDifficulty !== 'auto') {
      const difficultyMap = {
        'easy': Math.max(3, userAge - 2),
        'medium': userAge,
        'hard': Math.min(10, userAge + 2)
      };
      return difficultyMap[contentDifficulty] || userAge;
    }

    // Auto difficulty based on user progress
    const baseLevel = userAge;
    const { totalPoints = 0, completedSessions = 0, currentLevel = 1 } = userStats;

    // Progressive difficulty factors
    let difficultyBonus = 0;

    // Level-based progression
    if (currentLevel > 5) difficultyBonus += 1;
    if (currentLevel > 10) difficultyBonus += 1;

    // Points-based progression
    if (totalPoints > 500) difficultyBonus += 0.5;
    if (totalPoints > 1000) difficultyBonus += 0.5;

    // Session completion rate
    if (completedSessions > 10) difficultyBonus += 0.5;
    if (completedSessions > 25) difficultyBonus += 0.5;

    return Math.min(10, Math.max(3, Math.round(baseLevel + difficultyBonus)));
  }

  // Check if content should be refreshed
  shouldRefreshContent(userId) {
    const lastRefresh = this.lastContentRefresh[userId];
    if (!lastRefresh) return true;
    
    const hoursSinceRefresh = (Date.now() - lastRefresh.getTime()) / (1000 * 60 * 60);
    return hoursSinceRefresh >= this.refreshIntervalHours;
  }

  // Generate content recommendations using OpenAI
  async generateContentRecommendations(userAge, interests) {
    const prompt = this.buildContentPrompt(userAge, interests);

    const response = await this.openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{
        role: "system",
        content: `You are an expert content curator for children's educational media.
        You specialize in finding age-appropriate, engaging, and educational content from reputable sources.
        Always prioritize safety, educational value, and age-appropriateness.

        CRITICAL: You must ONLY use URLs from the specific domains provided in the user prompt.
        Do NOT create or guess URLs. Only use the exact base URLs provided and build specific paths from them.

        For each content item, provide:
        1. A specific, real URL from the approved domains
        2. An engaging, age-appropriate title
        3. A brief description (2-3 sentences)
        4. Relevant topics/tags
        5. Estimated duration in minutes
        6. Difficulty level (1-10 scale)

        Format as a JSON array with these exact fields: id, title, url, source, description, topics, estimatedDuration, difficultyLevel, type`
      }, {
        role: "user",
        content: prompt
      }],
      max_tokens: 2000,
      temperature: 0.7
    });

    const contentText = response.choices[0].message.content;
    return await this.parseContentRecommendations(contentText, userAge);
  }

  // Build the prompt for OpenAI
  buildContentPrompt(age, interests) {
    const interestsList = interests.length > 0 ? interests.join(', ') : 'general learning, science, nature, adventure';
    
    return `Recommend 12 educational content pieces for a ${age}-year-old child.

    IMPORTANT: Only provide URLs from these verified, reliable sources:

    For articles (4 required):
    - National Geographic Kids: https://kids.nationalgeographic.com/
    - Smithsonian's History Explorer: https://historyexplorer.si.edu/
    - NASA Kids: https://www.nasa.gov/audience/forkids/
    - BBC Bitesize: https://www.bbc.co.uk/bitesize/

    For videos (4 required):
    - Crash Course Kids: https://www.youtube.com/user/crashcoursekids
    - SciShow Kids: https://www.youtube.com/user/scishowkids
    - National Geographic Kids: https://www.youtube.com/user/NatGeoKids
    - TED-Ed: https://www.youtube.com/user/TEDEducation

    For books/stories (4 required):
    - Project Gutenberg Children's Books: https://www.gutenberg.org/browse/categories/1
    - International Children's Digital Library: http://en.childrenslibrary.org/
    - Storyline Online: https://www.storylineonline.net/
    - Open Library Kids: https://openlibrary.org/subjects/children
    
    Child's interests: ${interestsList}
    
    For each item, provide:
    - title: Clear, engaging title
    - url: Valid, working URL (verify these are real sources)
    - type: "article", "video", or "book"
    - source: The website/channel name
    - estimated_duration: Reading/viewing time in minutes
    - difficulty_level: 1-10 scale (${Math.max(1, age - 3)} to ${Math.min(10, age + 2)} for this age)
    - description: Brief, exciting description (2-3 sentences)
    - topics: Array of 2-3 relevant topics/subjects
    
    Format as valid JSON array. Ensure all URLs are from reputable, child-safe sources.
    Prioritize content that is both educational and engaging for a ${age}-year-old.`;
  }

  // Parse OpenAI response into structured content with URL validation
  async parseContentRecommendations(contentText, userAge) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = contentText.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON array found in response');
      }

      const contentArray = JSON.parse(jsonMatch[0]);

      // Basic validation first
      const basicValidatedContent = contentArray
        .filter(item => this.validateContentItem(item))
        .slice(0, 15); // Get a few extra in case some URLs fail

      // Validate URLs in parallel (but limit concurrent requests)
      const urlValidationPromises = basicValidatedContent.map(item =>
        this.verifyUrlAccessibility(item.url)
      );

      const urlValidationResults = await Promise.all(urlValidationPromises);

      // Filter out items with invalid URLs and sanitize the rest
      const validatedContent = basicValidatedContent
        .filter((item, index) => {
          const urlResult = urlValidationResults[index];
          if (!urlResult.isValid) {
            console.warn(`Removing content with invalid URL: ${item.title} - ${item.url} (${urlResult.error || 'HTTP ' + urlResult.statusCode})`);
            return false;
          }
          return true;
        })
        .map(item => this.sanitizeContentItem(item, userAge))
        .slice(0, 12); // Final limit to 12 items

      // Ensure we have content for each type
      const contentByType = {
        article: validatedContent.filter(item => item.type === 'article').slice(0, 4),
        video: validatedContent.filter(item => item.type === 'video').slice(0, 4),
        book: validatedContent.filter(item => item.type === 'book').slice(0, 4)
      };

      // Fill in with fallback content if needed
      Object.keys(contentByType).forEach(type => {
        while (contentByType[type].length < 4) {
          contentByType[type].push(this.getFallbackContentItem(type, userAge));
        }
      });

      return {
        articles: contentByType.article,
        videos: contentByType.video,
        books: contentByType.book,
        generatedAt: new Date(),
        userAge,
        urlValidationPerformed: true
      };
    } catch (error) {
      console.error('Error parsing content recommendations:', error);
      return this.getFallbackContent(userAge);
    }
  }

  // Validate a content item (basic structure validation)
  validateContentItem(item) {
    return (
      item &&
      typeof item.title === 'string' &&
      typeof item.url === 'string' &&
      ['article', 'video', 'book'].includes(item.type) &&
      typeof item.source === 'string' &&
      typeof item.estimated_duration === 'number' &&
      typeof item.difficulty_level === 'number' &&
      typeof item.description === 'string' &&
      Array.isArray(item.topics) &&
      this.isValidUrlFormat(item.url)
    );
  }

  // Check if URL format is valid
  isValidUrlFormat(urlString) {
    try {
      const url = new URL(urlString);
      // Only allow http and https protocols
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch {
      return false;
    }
  }

  // Verify that a URL is accessible (returns 200-299 status)
  async verifyUrlAccessibility(urlString) {
    return new Promise((resolve) => {
      try {
        const url = new URL(urlString);
        const protocol = url.protocol === 'https:' ? https : http;

        const options = {
          method: 'HEAD', // Use HEAD to avoid downloading content
          timeout: this.urlValidationTimeout,
          headers: {
            'User-Agent': 'BrainRipe-ContentValidator/1.0'
          }
        };

        const req = protocol.request(url, options, (res) => {
          // Consider 200-299 and 300-399 (redirects) as valid
          const isValid = res.statusCode >= 200 && res.statusCode < 400;
          resolve({
            isValid,
            statusCode: res.statusCode,
            url: urlString
          });
        });

        req.on('error', (error) => {
          console.warn(`URL validation failed for ${urlString}:`, error.message);
          resolve({
            isValid: false,
            error: error.message,
            url: urlString
          });
        });

        req.on('timeout', () => {
          req.destroy();
          console.warn(`URL validation timeout for ${urlString}`);
          resolve({
            isValid: false,
            error: 'Request timeout',
            url: urlString
          });
        });

        req.setTimeout(this.urlValidationTimeout);
        req.end();
      } catch (error) {
        resolve({
          isValid: false,
          error: error.message,
          url: urlString
        });
      }
    });
  }

  // Sanitize and enhance a content item
  sanitizeContentItem(item, userAge) {
    return {
      id: this.generateContentId(),
      title: item.title.substring(0, 200),
      url: item.url,
      type: item.type,
      source: item.source.substring(0, 100),
      estimatedDuration: Math.max(1, Math.min(60, item.estimated_duration)),
      difficultyLevel: Math.max(1, Math.min(10, item.difficulty_level)),
      description: item.description.substring(0, 500),
      topics: item.topics.slice(0, 5).map(topic => topic.substring(0, 50)),
      ageAppropriate: this.isAgeAppropriate(item, userAge),
      createdAt: new Date()
    };
  }

  // Check if content is age appropriate
  isAgeAppropriate(item, userAge) {
    const minAge = Math.max(6, userAge - 2);
    const maxAge = Math.min(16, userAge + 3);
    return item.difficulty_level >= minAge && item.difficulty_level <= maxAge;
  }

  // Generate unique content ID
  generateContentId() {
    return 'content_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Cache content for a user
  cacheContent(userId, content) {
    this.contentCache[userId] = {
      content,
      cachedAt: new Date()
    };
  }

  // Get cached content for a user
  getCachedContent(userId) {
    const cached = this.contentCache[userId];
    if (!cached) return null;
    
    const hoursSinceCached = (Date.now() - cached.cachedAt.getTime()) / (1000 * 60 * 60);
    if (hoursSinceCached >= this.refreshIntervalHours) {
      delete this.contentCache[userId];
      return null;
    }
    
    return cached.content;
  }

  // Get fallback content when AI fails
  getFallbackContent(userAge) {
    return {
      articles: [
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge)
      ],
      videos: [
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge)
      ],
      books: [
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge)
      ],
      generatedAt: new Date(),
      userAge,
      isFallback: true
    };
  }

  // Get a single fallback content item with verified working URLs
  getFallbackContentItem(type, userAge) {
    const fallbackContent = {
      article: {
        title: "Amazing Animals Around the World",
        url: "https://kids.nationalgeographic.com/animals/",
        source: "National Geographic Kids",
        estimatedDuration: 5,
        description: "Discover incredible facts about animals from around the globe with stunning photos and fun facts.",
        topics: ["animals", "nature", "science"]
      },
      video: {
        title: "NASA Kids Educational Content",
        url: "https://www.nasa.gov/audience/forstudents/k-4/",
        source: "NASA Kids",
        estimatedDuration: 8,
        description: "Explore space, rockets, and planets through NASA's educational resources for kids.",
        topics: ["space", "science", "exploration"]
      },
      book: {
        title: "Free Children's Books Online",
        url: "https://www.gutenberg.org/browse/categories/1",
        source: "Project Gutenberg",
        estimatedDuration: 15,
        description: "Access thousands of free classic children's books and stories online.",
        topics: ["stories", "reading", "literature"]
      }
    };

    const baseItem = fallbackContent[type];
    return {
      id: this.generateContentId(),
      ...baseItem,
      type,
      difficultyLevel: Math.max(3, Math.min(8, userAge)),
      ageAppropriate: true,
      createdAt: new Date(),
      isFallback: true
    };
  }

  // Clear cache for a user
  clearUserCache(userId) {
    delete this.contentCache[userId];
    delete this.lastContentRefresh[userId];
  }

  // Clear all cache
  clearAllCache() {
    this.contentCache = {};
    this.lastContentRefresh = {};
  }
}

module.exports = ContentCurator;
