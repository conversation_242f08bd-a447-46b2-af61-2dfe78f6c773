const mongoose = require('mongoose');

const contentSessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  contentUrl: {
    type: String,
    required: true,
    validate: {
      validator: function(url) {
        try {
          new URL(url);
          return true;
        } catch {
          return false;
        }
      },
      message: 'Please provide a valid URL'
    }
  },
  contentTitle: {
    type: String,
    required: true,
    maxlength: 200
  },
  contentType: {
    type: String,
    enum: ['article', 'video', 'book'],
    required: true
  },
  contentSource: {
    type: String,
    maxlength: 100
  },
  estimatedDuration: {
    type: Number, // in minutes
    min: 1,
    max: 300
  },
  difficultyLevel: {
    type: Number,
    min: 1,
    max: 10,
    default: 5
  },
  totalChunks: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  completedChunks: {
    type: Number,
    default: 0,
    min: 0
  },
  chunkDetails: [{
    chunkIndex: Number,
    content: String,
    completedAt: Date,
    timeSpent: Number, // in seconds
    pointsEarned: {
      type: Number,
      default: 10
    }
  }],
  pointsEarned: {
    type: Number,
    default: 0,
    min: 0
  },
  bonusPoints: {
    type: Number,
    default: 0,
    min: 0
  },
  status: {
    type: String,
    enum: ['active', 'completed', 'paused', 'abandoned'],
    default: 'active'
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: {
    type: Date
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  totalTimeSpent: {
    type: Number, // in seconds
    default: 0
  },
  tags: [{
    type: String,
    maxlength: 50
  }],
  userRating: {
    type: Number,
    min: 1,
    max: 5
  },
  userFeedback: {
    type: String,
    maxlength: 500
  }
}, {
  timestamps: true
});

// Indexes for performance
contentSessionSchema.index({ userId: 1, status: 1 });
contentSessionSchema.index({ userId: 1, createdAt: -1 });
contentSessionSchema.index({ contentType: 1, status: 1 });
contentSessionSchema.index({ completedAt: -1 });

// Virtual for completion percentage
contentSessionSchema.virtual('completionPercentage').get(function() {
  return this.totalChunks > 0 ? Math.round((this.completedChunks / this.totalChunks) * 100) : 0;
});

// Virtual for average time per chunk
contentSessionSchema.virtual('averageTimePerChunk').get(function() {
  return this.completedChunks > 0 ? Math.round(this.totalTimeSpent / this.completedChunks) : 0;
});

// Method to complete a chunk
contentSessionSchema.methods.completeChunk = function(chunkIndex, timeSpent = 0) {
  // Check if chunk is already completed
  const existingChunk = this.chunkDetails.find(chunk => chunk.chunkIndex === chunkIndex);
  if (existingChunk) {
    throw new Error('Chunk already completed');
  }
  
  // Calculate points based on difficulty and time
  const basePoints = 10;
  const difficultyMultiplier = this.difficultyLevel * 0.2;
  const timeBonus = Math.min(timeSpent / 60, 5); // Max 5 bonus points for time
  const chunkPoints = Math.round(basePoints * (1 + difficultyMultiplier) + timeBonus);
  
  // Add chunk details
  this.chunkDetails.push({
    chunkIndex,
    completedAt: new Date(),
    timeSpent,
    pointsEarned: chunkPoints
  });
  
  // Update session stats
  this.completedChunks += 1;
  this.pointsEarned += chunkPoints;
  this.totalTimeSpent += timeSpent;
  this.lastAccessedAt = new Date();
  
  // Check if session is completed
  if (this.completedChunks >= this.totalChunks) {
    this.status = 'completed';
    this.completedAt = new Date();
    
    // Award completion bonus
    const completionBonus = Math.round(this.totalChunks * 5);
    this.bonusPoints += completionBonus;
    this.pointsEarned += completionBonus;
  }
  
  return {
    chunkPoints,
    totalPoints: this.pointsEarned,
    completionPercentage: this.completionPercentage,
    isCompleted: this.status === 'completed',
    bonusAwarded: this.status === 'completed' ? this.bonusPoints : 0
  };
};

// Method to pause session
contentSessionSchema.methods.pauseSession = function() {
  if (this.status === 'active') {
    this.status = 'paused';
    this.lastAccessedAt = new Date();
  }
};

// Method to resume session
contentSessionSchema.methods.resumeSession = function() {
  if (this.status === 'paused') {
    this.status = 'active';
    this.lastAccessedAt = new Date();
  }
};

// Method to abandon session
contentSessionSchema.methods.abandonSession = function() {
  if (this.status === 'active' || this.status === 'paused') {
    this.status = 'abandoned';
    this.lastAccessedAt = new Date();
    this.completedAt = new Date();
  }
};

// Method to abandon session
contentSessionSchema.methods.abandonSession = function() {
  if (this.status === 'active' || this.status === 'paused') {
    this.status = 'abandoned';
    this.lastAccessedAt = new Date();
  }
};

// Static method to get user's active sessions
contentSessionSchema.statics.getActiveSessions = function(userId) {
  return this.find({
    userId,
    status: { $in: ['active', 'paused'] }
  }).sort({ lastAccessedAt: -1 });
};

// Static method to get user's completed sessions
contentSessionSchema.statics.getCompletedSessions = function(userId, limit = 10) {
  return this.find({
    userId,
    status: 'completed'
  }).sort({ completedAt: -1 }).limit(limit);
};

// Static method to get user's reading statistics
contentSessionSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        completedSessions: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        totalPointsEarned: { $sum: '$pointsEarned' },
        totalTimeSpent: { $sum: '$totalTimeSpent' },
        totalChunksCompleted: { $sum: '$completedChunks' },
        averageRating: { $avg: '$userRating' }
      }
    }
  ]);

  return stats[0] || {
    totalSessions: 0,
    completedSessions: 0,
    totalPointsEarned: 0,
    totalTimeSpent: 0,
    totalChunksCompleted: 0,
    averageRating: 0
  };
};

module.exports = mongoose.model('ContentSession', contentSessionSchema);
