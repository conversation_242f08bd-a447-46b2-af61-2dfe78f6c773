const express = require('express');
const { User, ContentSession } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get user progress statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.userId;
    
    // Get user stats from ContentSession
    const stats = await ContentSession.getUserStats(userId);
    
    // Get additional user data
    const user = await User.findById(userId).select('totalPoints weeklyPoints currentLevel badges');
    
    // Calculate additional metrics
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    const todaySessions = await ContentSession.countDocuments({
      userId,
      createdAt: { $gte: today, $lt: tomorrow },
      status: 'completed'
    });
    
    // Get streak information
    const streak = await calculateReadingStreak(userId);
    
    // Get content type breakdown
    const contentTypeStats = await ContentSession.aggregate([
      { $match: { userId: userId, status: 'completed' } },
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 },
          totalPoints: { $sum: '$pointsEarned' },
          totalTime: { $sum: '$totalTimeSpent' }
        }
      }
    ]);
    
    const enhancedStats = {
      ...stats,
      todaySessions,
      readingStreak: streak,
      contentTypeBreakdown: contentTypeStats,
      level: user.currentLevel,
      totalPoints: user.totalPoints,
      weeklyPoints: user.weeklyPoints,
      badgeCount: user.badges.length
    };
    
    res.json({
      success: true,
      stats: enhancedStats
    });
    
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      error: 'Failed to get user statistics',
      code: 'GET_STATS_ERROR'
    });
  }
});

// Add points to user (used internally by content completion)
router.post('/add-points', authenticateToken, async (req, res) => {
  try {
    const { points, source = 'content_completion' } = req.body;
    
    if (!points || points <= 0) {
      return res.status(400).json({
        error: 'Valid points amount required',
        code: 'INVALID_POINTS'
      });
    }
    
    const user = req.user;
    const levelUpResult = user.addPoints(points);
    await user.save();
    
    // Log the points addition
    await logPointsActivity(user._id, points, source);
    
    res.json({
      success: true,
      pointsAdded: points,
      totalPoints: user.totalPoints,
      weeklyPoints: user.weeklyPoints,
      levelUpResult
    });
    
  } catch (error) {
    console.error('Add points error:', error);
    res.status(500).json({
      error: 'Failed to add points',
      code: 'ADD_POINTS_ERROR'
    });
  }
});

// Get user's badges
router.get('/badges', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.userId).select('badges');
    
    res.json({
      success: true,
      badges: user.badges || [],
      badgeCount: user.badges?.length || 0
    });
    
  } catch (error) {
    console.error('Get badges error:', error);
    res.status(500).json({
      error: 'Failed to get badges',
      code: 'GET_BADGES_ERROR'
    });
  }
});

// Get available achievements
router.get('/achievements', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.userId);
    const earnedBadgeIds = user.badges.map(badge => badge.badgeId);
    
    // Get user stats for achievement calculations
    const stats = await ContentSession.getUserStats(req.userId);
    
    const allAchievements = [
      {
        id: 'first_completion',
        name: 'First Steps',
        description: 'Complete your first reading session',
        icon: '🎯',
        requirement: 'Complete 1 session',
        progress: Math.min(stats.completedSessions, 1),
        target: 1,
        earned: earnedBadgeIds.includes('first_completion')
      },
      {
        id: 'news_reader',
        name: 'News Explorer',
        description: 'Complete 5 news articles',
        icon: '📰',
        requirement: 'Complete 5 articles',
        progress: await getContentTypeCompletions(req.userId, 'article'),
        target: 5,
        earned: earnedBadgeIds.includes('news_reader')
      },
      {
        id: 'video_watcher',
        name: 'Video Scholar',
        description: 'Complete 5 educational videos',
        icon: '🎥',
        requirement: 'Complete 5 videos',
        progress: await getContentTypeCompletions(req.userId, 'video'),
        target: 5,
        earned: earnedBadgeIds.includes('video_watcher')
      },
      {
        id: 'book_reader',
        name: 'Book Lover',
        description: 'Complete 5 books or stories',
        icon: '📚',
        requirement: 'Complete 5 books',
        progress: await getContentTypeCompletions(req.userId, 'book'),
        target: 5,
        earned: earnedBadgeIds.includes('book_reader')
      },
      {
        id: 'point_collector_100',
        name: 'Point Collector',
        description: 'Earn 100 total points',
        icon: '⭐',
        requirement: 'Earn 100 points',
        progress: Math.min(user.totalPoints, 100),
        target: 100,
        earned: earnedBadgeIds.includes('point_collector_100')
      },
      {
        id: 'point_collector_500',
        name: 'Point Master',
        description: 'Earn 500 total points',
        icon: '🌟',
        requirement: 'Earn 500 points',
        progress: Math.min(user.totalPoints, 500),
        target: 500,
        earned: earnedBadgeIds.includes('point_collector_500')
      },
      {
        id: 'point_collector_1000',
        name: 'Point Champion',
        description: 'Earn 1000 total points',
        icon: '💫',
        requirement: 'Earn 1000 points',
        progress: Math.min(user.totalPoints, 1000),
        target: 1000,
        earned: earnedBadgeIds.includes('point_collector_1000')
      },
      {
        id: 'streak_reader',
        name: 'Consistent Reader',
        description: 'Read for 7 days in a row',
        icon: '🔥',
        requirement: '7 day streak',
        progress: Math.min(await calculateReadingStreak(req.userId), 7),
        target: 7,
        earned: earnedBadgeIds.includes('streak_reader')
      }
    ];
    
    res.json({
      success: true,
      achievements: allAchievements
    });
    
  } catch (error) {
    console.error('Get achievements error:', error);
    res.status(500).json({
      error: 'Failed to get achievements',
      code: 'GET_ACHIEVEMENTS_ERROR'
    });
  }
});

// Get user's reading streak
router.get('/streak', authenticateToken, async (req, res) => {
  try {
    const streak = await calculateReadingStreak(req.userId);
    
    res.json({
      success: true,
      streak,
      message: streak > 0 ? `You're on a ${streak} day streak!` : 'Start reading to begin your streak!'
    });
    
  } catch (error) {
    console.error('Get streak error:', error);
    res.status(500).json({
      error: 'Failed to get reading streak',
      code: 'GET_STREAK_ERROR'
    });
  }
});

// Get leaderboard position
router.get('/leaderboard-position', authenticateToken, async (req, res) => {
  try {
    const WeeklyLeaderboard = require('../models/WeeklyLeaderboard');
    const position = await WeeklyLeaderboard.getUserCurrentWeekPosition(req.userId);
    
    if (position) {
      res.json({
        success: true,
        position: {
          rank: position.rank,
          weeklyPoints: position.weeklyPoints,
          rankChange: position.rankChange,
          previousRank: position.previousWeekRank
        }
      });
    } else {
      res.json({
        success: true,
        position: null,
        message: 'Not yet ranked this week. Start reading to join the leaderboard!'
      });
    }
    
  } catch (error) {
    console.error('Get leaderboard position error:', error);
    res.status(500).json({
      error: 'Failed to get leaderboard position',
      code: 'GET_POSITION_ERROR'
    });
  }
});

// Update user theme
router.post('/update-theme', authenticateToken, async (req, res) => {
  try {
    const { theme } = req.body;

    const validThemes = ['default', 'ocean', 'forest', 'space', 'rainbow'];
    if (!validThemes.includes(theme)) {
      return res.status(400).json({
        error: 'Invalid theme',
        code: 'INVALID_THEME'
      });
    }

    const user = await User.findById(req.userId);
    user.profileCustomizations.theme = theme;
    await user.save();

    res.json({
      success: true,
      message: 'Theme updated successfully'
    });

  } catch (error) {
    console.error('Update theme error:', error);
    res.status(500).json({
      error: 'Failed to update theme',
      code: 'UPDATE_THEME_ERROR'
    });
  }
});

// Update user preferences
router.post('/update-preferences', authenticateToken, async (req, res) => {
  try {
    const { contentDifficulty, sessionLength, preferredContentTypes } = req.body;

    const user = await User.findById(req.userId);

    if (contentDifficulty && ['auto', 'easy', 'medium', 'hard'].includes(contentDifficulty)) {
      user.preferences.contentDifficulty = contentDifficulty;
    }

    if (sessionLength && ['short', 'medium', 'long'].includes(sessionLength)) {
      user.preferences.sessionLength = sessionLength;
    }

    if (preferredContentTypes && Array.isArray(preferredContentTypes)) {
      const validTypes = preferredContentTypes.filter(type =>
        ['article', 'video', 'book'].includes(type)
      );
      if (validTypes.length > 0) {
        user.preferences.preferredContentTypes = validTypes;
      }
    }

    await user.save();

    res.json({
      success: true,
      message: 'Preferences updated successfully',
      preferences: user.preferences
    });

  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({
      error: 'Failed to update preferences',
      code: 'UPDATE_PREFERENCES_ERROR'
    });
  }
});

// Helper function to calculate reading streak
async function calculateReadingStreak(userId) {
  try {
    const today = new Date();
    let currentDate = new Date(today);
    currentDate.setHours(0, 0, 0, 0);
    
    let streak = 0;
    
    // Check each day going backwards
    for (let i = 0; i < 30; i++) { // Check up to 30 days
      const dayStart = new Date(currentDate);
      const dayEnd = new Date(currentDate);
      dayEnd.setDate(dayEnd.getDate() + 1);
      
      const sessionsThisDay = await ContentSession.countDocuments({
        userId,
        status: 'completed',
        completedAt: { $gte: dayStart, $lt: dayEnd }
      });
      
      if (sessionsThisDay > 0) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }
    
    return streak;
  } catch (error) {
    console.error('Calculate streak error:', error);
    return 0;
  }
}

// Helper function to get content type completions
async function getContentTypeCompletions(userId, contentType) {
  try {
    return await ContentSession.countDocuments({
      userId,
      contentType,
      status: 'completed'
    });
  } catch (error) {
    console.error('Get content type completions error:', error);
    return 0;
  }
}

// Helper function to log points activity
async function logPointsActivity(userId, points, source) {
  try {
    // In a full implementation, you might want to create a PointsLog model
    // For now, we'll just log to console
    console.log(`User ${userId} earned ${points} points from ${source}`);
  } catch (error) {
    console.error('Log points activity error:', error);
  }
}

module.exports = router;
